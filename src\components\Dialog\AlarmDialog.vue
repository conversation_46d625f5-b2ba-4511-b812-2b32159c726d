<template>
  <div class="soilAlarmDialog dialogStyle">
    <el-dialog
      title="报警记录"
      width="78%"
      :visible.sync="dialogVisible"
      center
      :show-close="false"
      @open="dialogOpen"
      @close="dialogClose"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
        <div class="line"></div>
        <div class="close" @click="closeDialog">
          <img src="../../assets/image/centralControlPlatform/close.png" alt="">
        </div>
        <div class="handleBox">
          <div class="handleBox_item formStyle">
            <el-select
            v-model="alarmType" 
            placeholder="请选择报警类型" 
            @change="alarmTypeChange"
            popper-class="selectStyle_list">
              <el-option
                v-for="item in alarmTypeData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                >
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item formStyle">
            <el-select
            v-model="type" 
            clearable 
            placeholder="请选择报警状态" 
            popper-class="selectStyle_list"
            @change="typeChange">
              <el-option
                v-for="item in typeData"
                :key="item.value"
                :label="item.key"
                :value="item.value"
                >
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item formStyle" v-if="alarmType==2">
            <el-select 
            v-model="areaId" 
            clearable 
            placeholder="请选择种植区域"
            popper-class="selectStyle_list">
              <el-option
                v-for="item in areaIdData"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item formStyle">
            <el-date-picker
              v-model="time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              popper-class='datePickerStyle'
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </div>
          <div class="handleBox_item searchButtonStyle">
            <el-button @click="search">查询</el-button>
          </div>
        </div>
         <div class="tableBox tableStyle" v-show="alarmType==1">
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            height="405px">
            <el-table-column
              type="index" 
              label="序号" 
              align="center" 
              width="80">
            </el-table-column>
            <el-table-column
              align="center"
              label="报警类型"
              width="120">
                <template>
                  <div>
                    {{alarmType | typeFormat}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
              prop="areaName"
              align="center"
              label="种植区域">
            </el-table-column>
            <el-table-column
              align="center"
              label="报警状态">
              <template slot-scope="scoped">
                <div>
                  {{ scoped.row.alarmType | alarmTypeFormat }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="报警数值">
              <template slot-scope="scope">
                <div >
                  <el-tooltip popper-class="tooltipStyle" placement="top">
                    <div slot="content">
                      <div class="item">
                        温度阈值0℃~40℃
                      </div>
                    </div>
                    <div>{{scope.row.value}}</div>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="startTime"
              align="center"
              label="起始时间">
            </el-table-column>
            <el-table-column
              prop="endTime"
              align="center"
              label="结束时间">
            </el-table-column>
          </el-table>
        </div>
        <div class="tableBox tableStyle" v-show="alarmType==2">
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            height="405px">
            <el-table-column
              type="index" 
              label="序号" 
              align="center" 
              width="80">
            </el-table-column>
            <el-table-column
              align="center"
              label="报警类型"
              width="120">
              <template>
                <div>
                  {{alarmType | typeFormat}}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="areaName"
              align="center"
              label="种植区域">
            </el-table-column>
            <el-table-column
              align="center"
              label="报警层数"
              width="120">
              <template slot-scope="scoped">
                <div>
                  {{ scoped.row.layer | soilLayerFormat }}
                </div>
              </template>
            </el-table-column>
             <el-table-column
              align="center"
              label="报警状态">
              <template slot-scope="scoped">
                <div>
                  {{ scoped.row.alarmType | alarmTypeFormat }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="value"
              align="center"
              label="报警数值">
            </el-table-column>
            <el-table-column
              prop="startTime"
              align="center"
              label="起始时间">
            </el-table-column>
            <el-table-column
              prop="endTime"
              align="center"
              label="结束时间">
            </el-table-column>
            <!-- <el-table-column
              align="center"
              label="操作"
              width="100">
              <template slot-scope="scoped">
                <span class="viewData" @click="viewDetails(scoped.row)">
                查看详情
              </span>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
        <div class="tableBox tableStyle" v-show="alarmType==4">
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            height="405px">
            <el-table-column
              type="index" 
              label="序号" 
              align="center" 
              width="80">
            </el-table-column>
            <el-table-column
              align="center"
              label="报警类型"
              width="120">
              <template>
                <div>
                  {{alarmType | typeFormat}}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="areaName"
              align="center"
              label="种植区域"
              width="120">
            </el-table-column>
            <el-table-column
              align="center"
              label="监测数值"
              width="150">
              <template slot-scope="scoped">
                <div>
                  <el-tooltip popper-class="tooltipStyle" placement="bottom">
                    <div slot="content">
                      <div class="item">
                        {{scoped.row.kind | kindFormat}}≥{{scoped.row.value}}只/亩
                      </div>
                    </div>
                    <div>
                      {{scoped.row.value}}/亩
                      <img src="../../assets/image/centralControlPlatform/doubt-tip.png" alt="" />
                    </div>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              width="80" 
              label="种类">
              <template slot-scope="scoped">
                <div>
                  {{ scoped.row.kind | kindFormat }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="count"
              align="center" 
              width="80"
              label="数量">
            </el-table-column>
            <el-table-column
              align="center"
              label="报警状态">
              <template slot-scope="scoped">
                <div>
                  {{ scoped.row.alarmType | alarmTypeFormat }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="startTime"
              align="center" 
              label="报警时间">
            </el-table-column>
            <el-table-column
              prop="endTime"
              align="center" 
              label="报警结束时间">
            </el-table-column>
            <el-table-column
              align="center" 
              label="图片">
                <template slot-scope="scoped">
                  <img class="img" :src="scoped.row.picUrl" alt="">
                </template>
            </el-table-column>
          </el-table>
        </div>
    </el-dialog>
  </div>
</template>
<script>
import 报警具体类型 from "../../jaxrs/constants/com.zny.ia.constant.DeviceConstant$报警具体类型";
import SoilService from '../../jaxrs/concrete/com.zny.ia.api.SoilService.js';
import WeatherService from '../../jaxrs/concrete/com.zny.ia.api.WeatherService.js';
import WormCaseService from '../../jaxrs/concrete/com.zny.ia.api.WormCaseService.js';
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService.js';
export default {
  data(){
    return{
      dialogVisible:false,
      alarmType:1,
      alarmTypeData:[
        {label:"气象报警",value:1},
        {label:"土壤报警",value:2},
        {label:"虫情报警",value:4},
      ],
      type:null,
      typeData:[],
      areaId:null,
      areaIdData:[],
      time:[],
      tableData: []
    }
  },
  mounted(){
    // 获取区域列表
    this.getAreaList()

    this.listen('AlarmDialogOpen', data => {
      console.log('AlarmDialogOpen 接收到的数据:', data)
      // 兼容旧格式（直接传递type）和新格式（传递对象）
      if (typeof data === 'number') {
        this.alarmType = data
        this.areaId = null
      } else {
        this.alarmType = data.type
        this.areaId = data.areaId
      }

      console.log('设置的 alarmType:', this.alarmType, 'areaId:', this.areaId)
      this.dialogVisible=true
      switch (this.alarmType) {
        case 1:
          this.getMeteorologicalListAlarmRecord()
          break
        case 2:
          this.getSoilListAlarmRecord()
          break
        case 4:
          this.getInsectSituationListAlarmRecord()
          break
      }
      this.getAlarmType()
    })
  },
  methods:{
    // 弹窗打开
    dialogOpen(){
      this.type=null
    },
    // 弹窗关闭
    dialogClose(){
      this.resetData()
    },
    // 关闭弹窗
    closeDialog(){
      this.dialogVisible = false
    },
    // 重置数据
    resetData(){
      this.alarmType = 1
      this.type = null
      this.areaId = null
      this.time = []
      this.tableData = []
      this.typeData = []
    },
    // 报警类型改变
    alarmTypeChange(){
      this.search()
      this.getAlarmType()
    },
    // 报警状态列表
    getAlarmType(){
      const mapping = {"1":[1,2,3,4],"2":[5,6,7,8,9,10,11,12,13,14,15,16,17],"3":[],"4":[18,19,20,21,22],"5":[],"6":[]}
      this.typeData=报警具体类型._toArray(mapping[this.alarmType])
      // 指标类型._toArray().forEach(a =>{
      //     console.log(报警具体类型._toArray(mapping[a.value]))
      // })
    },
    // 报警状态改变
    typeChange(val){
      this.type = val =='' ? null : val
    },
    // 查询
    search(){
      switch (this.alarmType) {  
        case 1:
          this.getMeteorologicalListAlarmRecord()
          break
        case 2:
          this.getSoilListAlarmRecord()
          break
        case 4:
          this.getInsectSituationListAlarmRecord()
          break
      }
    },
    // 气象报警记录
    getMeteorologicalListAlarmRecord(){
      let po={
        num:1,
        pageSize:100,
        condition:{
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],
          type:this.type
        }
      }
      WeatherService.weatherAlarmRecord(po)
      .then(res=>{
        let list = res.list || []
        console.log('气象报警原始数据:', list)
        console.log('当前 areaId:', this.areaId)
        // 如果有地块ID，进行前端过滤
        if (this.areaId) {
          list = list.filter(item => {
            console.log('数据项 areaId:', item.areaId, '匹配结果:', item.areaId == this.areaId)
            return item.areaId == this.areaId
          })
        }
        console.log('过滤后的数据:', list)
        this.tableData = list
      })
    },
    // 虫情报警记录
    getInsectSituationListAlarmRecord(){
      let po={
        num:1,
        pageSize:100,
        condition:{
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],
          type:this.type
        }
      }
      WormCaseService.wormCaseAlarmRecord(po)
      .then(res=>{
        this.tableData=res.list
      })
    },
    // 土壤报警记录
    getSoilListAlarmRecord(){
      let po={
        num:1,
        pageSize:100,
        condition:{
          areaId:this.areaId,
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],
          type:this.type
        }
      }
      SoilService.listAlarmRecord(po)
      .then(res=>{
        this.tableData=res.list
      })
    },
    // 获取区域列表
    getAreaList(){
      CommonService.allAreaOfCurrentUserManage()
      .then(res => {
        this.areaIdData = res.map(area => ({
          label: area.areaName,
          value: area.areaId
        }))
      })
    },
    // 查看详情
    viewDetails(){

    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/meteorologicalAlarmDialog.less';
@import '../../assets/css/Dialog/soilAlarmDialog.less';
</style>