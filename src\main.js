import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import eventBus from './eventBus'
import concrete from './jaxrs/concrete'

// 地图
import znyMap from '@zny/vue-map'
import AMapProvider from '@zny/vue-map/AMapProvider.js'
Vue.use(znyMap, { mapProvider: AMapProvider })
//注册echarts组件
import znyEchart from '../src/components/eChart.vue'
Vue.component('znyEchart', znyEchart)
//
import znyRC from '@zny/root-container'
Vue.use(znyRC)
// 引入elementUI
import { Message } from 'element-ui'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN'

// 自定义中文语言包，修改分页组件的"前往"为"跳至"
const customLocale = {
  ...locale,
  el: {
    ...locale.el,
    pagination: {
      ...locale.el.pagination,
      goto: '跳至'
    }
  }
}

Vue.use(ElementUI, { locale: customLocale })
// 引入axios
import axios from 'axios'
Vue.prototype.axios = axios
//  引入Echarts
import * as echarts from 'echarts'
Vue.prototype.$echarts = echarts
//图片路径
Vue.prototype.baseUrl = 'http://*************:8075' //杜
//视频

import Video from 'video.js'
import 'video.js/dist/video-js.css'

Vue.prototype.$video = Video

import vueSeamlessScroll from 'vue-seamless-scroll' // 循环滚动
Vue.use(vueSeamlessScroll)

import 指标类型 from '../src/jaxrs/constants/com.zny.ia.constant.DeviceConstant$指标类型.js';
Vue.filter("equipmentTypeFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 指标类型._lableOf(msg)
});


import 设备状态 from '../src/jaxrs/constants/com.zny.ia.constant.DeviceConstant$设备状态.js';
Vue.filter("stateFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 设备状态._lableOf(msg)
});

import 生产计划类型 from '../src/jaxrs/constants/com.zny.ia.constant.ProdConstant$生产计划类型.js';
Vue.filter("productionPlanTypeFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 生产计划类型._lableOf(msg)
});

import 农作物种类 from '../src/jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类.js';
Vue.filter("cropFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 农作物种类._lableOf(msg)
});

import 风向信息 from '../src/jaxrs/constants/com.zny.ia.constant.DeviceConstant$风向信息.js';
Vue.filter("windDirectionFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 风向信息._lableOf(msg)
});

import 虫情种类 from '../src/jaxrs/constants/com.zny.ia.constant.DeviceConstant$虫情种类.js';
Vue.filter("kindFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 虫情种类._lableOf(msg)
});

import 土壤层 from '../src/jaxrs/constants/com.zny.ia.constant.ProdConstant$土壤层.js';
Vue.filter("soilLayerFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 土壤层._lableOf(msg)
})
import 农作物生长期 from '../src/jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物生长期';
Vue.filter("growthFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 农作物生长期._lableOf(msg);
})
import 叶色状况 from '../src/jaxrs/constants/com.zny.ia.constant.ProdConstant$叶色状况';
Vue.filter("leafColorFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 叶色状况._lableOf(msg)
})
import 苗株长相 from '../src/jaxrs/constants/com.zny.ia.constant.ProdConstant$苗株长相';
Vue.filter("plantLookFormat", function (msg) {
    if(msg!=null && msg.toString() != '') return 苗株长相._lableOf(msg)
})
Vue.filter('typeFormat', function(msg) {
    if (msg == 0) {
        return '全部报警'
    } else if (msg == 1) {
        return '气象报警'
    } else if (msg == 2) {
        return '土壤报警'
    } else if (msg == 4) {
        return '虫情报警'
    } else {
        return ''
    }
})

import 报警具体类型 from '../src/jaxrs/constants/com.zny.ia.constant.DeviceConstant$报警具体类型.js'
Vue.filter('alarmTypeFormat', function(msg) {
    if(msg!=null && msg.toString() != '') return 报警具体类型._lableOf(msg)
})
import 水质检测项 from '../src/jaxrs/constants/com.zny.ia.constant.DeviceConstant$水质检测项'
Vue.filter('waterQualityDetectionFormat', function(msg) {
    if(msg!=null && msg.toString() != '') return 水质检测项._lableOf(msg)
})
import 生产计划状态 from '../src/jaxrs/constants/com.zny.ia.constant.ProdConstant$生产计划状态'
Vue.filter('productionPlanFormat', function(msg) {
    if(msg!=null && msg.toString() != '') return 生产计划状态._lableOf(msg)
})
Vue.prototype.zEmit = function(event, data) {
    this.$nextTick(() => {
        if (data === undefined) {
            eventBus.$emit(event)
        } else {
            eventBus.$emit(event, data)
        }
    })
}

Vue.prototype.listen = function(event, handle) {
    this.$$eventHandlers = this.$$eventHandlers || {}
    if (this.$$eventHandlers[event]) {
        console.warn('重复的消息侦听' + event)
    }
    this.$$eventHandlers[event] = handle

    eventBus.$on(event, this.$$eventHandlers[event])
}

Vue.prototype.removeAllListener = function() {
    if (this.$$eventHandlers) {
        for (let event in this.$$eventHandlers) {
            eventBus.$off(event, this.$$eventHandlers[event])
        }
    }
}

Vue.config.productionTip = false

concrete.configure({
    root: '/ia',
    onError: function(code, msg) {
        if (code == 101104 || code == 1003 || code == 1004 || code == 1005) {
            if (!store.state.isLogout) {
                Message.error(msg)
            }
            router.replace({ path: '/' })
        } else if (code == 99999) {
            // eslint-disable no-empty
        } else {
            Message.error(msg)
        }
    },
    storage: localStorage,
    // grable: process.env.NODE_ENV === 'production',
    headers: {
        'x-tenant-id': "1"
    },
    grable: false,
    pollingTimeout: 10,
    globalTokenKey: 'token',
    onBroadcast: function(msgId, host, subject, data) {
        console.log(['msgId: ', msgId, '; host: ', host, '; subject: ', subject, '; data: ', data].join(''))
    },
})

new Vue({
    router,
    store,
    render: h => h(App),
}).$mount('#app')
